/**
 * Enhanced Intelligent Autonomous Supervisor API Route
 * 
 * Implements the revolutionary research pipeline:
 * 1. Quick Tavily search for exact topic
 * 2. Parallel web scraping of 10 URLs
 * 3. Three-stage analysis (topic understanding, data extraction, query generation)
 * 4. Intelligent planning based on analysis
 * 5. Traditional workflow execution with intelligence
 */

import { NextRequest, NextResponse } from 'next/server';
import { EnhancedIntelligentAutonomousSupervisor2025 } from '@/lib/agents/autonomous/EnhancedIntelligentAutonomousSupervisor2025';

export async function POST(req: NextRequest) {
  try {
    const { goal, config } = await req.json();

    if (!goal) {
      return NextResponse.json(
        { error: 'Goal is required' },
        { status: 400 }
      );
    }

    console.log(`🧠 Enhanced Intelligence Pipeline starting for: "${goal}"`);

    // Initialize enhanced intelligent supervisor
    const supervisor = new EnhancedIntelligentAutonomousSupervisor2025(config);

    // Execute revolutionary research pipeline
    const result = await supervisor.executeAutonomous(goal);

    console.log(`✅ Enhanced Intelligence Pipeline completed`);
    console.log(`📊 Results:`, {
      success: result.success,
      qualityScore: result.qualityScore,
      executionTime: `${Math.round(result.executionTime / 1000)}s`,
      insights: result.intelligenceInsights
    });

    return NextResponse.json({
      success: true,
      data: {
        result: result.result,
        qualityScore: result.qualityScore,
        executionTime: result.executionTime,
        insights: result.intelligenceInsights,
        methodology: 'Enhanced Intelligent Autonomous Supervisor 2025',
        pipeline: [
          'Quick Tavily search for exact topic',
          'Parallel web scraping of 10 URLs',
          'Three-stage analysis pipeline',
          'Intelligent planning based on analysis',
          'Traditional workflow with intelligence'
        ]
      }
    });

  } catch (error) {
    console.error('❌ Enhanced Intelligence Pipeline error:', error);
    
    return NextResponse.json(
      { 
        error: 'Enhanced Intelligence Pipeline failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    name: 'Enhanced Intelligent Autonomous Supervisor 2025',
    description: 'Revolutionary research pipeline with comprehensive competitive intelligence',
    features: [
      'Quick Tavily search for exact topic understanding',
      'Parallel web scraping of 10 competitor URLs',
      'Three-stage analysis: topic understanding, data extraction, query generation',
      'Intelligent planning based on comprehensive analysis',
      'Works for all article types (alternatives, guides, tutorials, listicles)',
      'Superior content generation with competitive intelligence'
    ],
    pipeline: {
      'Phase 1': 'Quick Tavily Search - Immediate topic research',
      'Phase 2': 'Parallel Web Scraping - 10 URLs scraped simultaneously',  
      'Phase 3': 'Three-Stage Analysis - Deep competitive intelligence',
      'Phase 4': 'Intelligent Planning - Strategic content planning',
      'Phase 5': 'Enhanced Execution - Traditional workflow with intelligence'
    },
    analysisStages: {
      'Stage 1': 'Topic Understanding & Context Analysis',
      'Stage 2': 'Data Points & Statistics Extraction', 
      'Stage 3': 'Query Generation Intelligence'
    }
  });
} 