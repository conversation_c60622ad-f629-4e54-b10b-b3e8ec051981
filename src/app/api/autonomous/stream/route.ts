import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { EnhancedIntelligentAutonomousSupervisor2025 } from '@/lib/agents/autonomous/EnhancedIntelligentAutonomousSupervisor2025';

export async function POST(req: NextRequest) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ 
        error: 'Unauthorized - Authentication required',
        code: 'AUTH_REQUIRED'
      }, { status: 401 });
    }

    // Parse and validate request
    const body = await req.json().catch(() => ({}));
    const { goal, config = {} } = body;
    
    // Input validation
    if (!goal || typeof goal !== 'string' || goal.trim().length < 5) {
      return NextResponse.json({ 
        error: 'Invalid goal - Goal must be at least 5 characters long',
        code: 'INVALID_INPUT'
      }, { status: 400 });
    }

    // Sanitize goal
    const sanitizedGoal = goal.trim().substring(0, 500);

    console.log('🚀 Enhanced Intelligent Autonomous Supervisor 2025 - Starting revolutionary research pipeline');
    console.log(`👤 User: ${session.user.email}`);
    console.log(`🎯 Goal: "${sanitizedGoal}"`);
    
    // Enhanced configuration
    const enhancedConfig = {
      maxIterations: Math.min(config.maxIterations || 15, 25),
      maxRetries: Math.min(config.maxRetries || 3, 5),
      timeoutMs: Math.min(config.timeoutMs || 900000, 1800000), // Max 30 minutes
      qualityThreshold: Math.max(Math.min(config.qualityThreshold || 85, 95), 70),
      enableSelfReflection: config.enableSelfReflection !== false,
      enableQualityGates: config.enableQualityGates !== false,
      enableRecursionLimits: config.enableRecursionLimits !== false,
      verboseLogging: config.verboseLogging !== false
    };

    // Create the streaming response
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      start(controller) {
        // Helper function to send SSE data
        const sendData = (data: any) => {
          const message = `data: ${JSON.stringify(data)}\n\n`;
          controller.enqueue(encoder.encode(message));
        };

        // Start execution process
        (async () => {
          const startTime = Date.now();
          let currentPhase = 'initialization';
          let progress = 0;
          
          try {
            // Send initialization message
            sendData({
              type: 'status',
              phase: 'initialization',
              message: 'Initializing Enhanced Autonomous Supervisor 2025...',
              progress: 0,
              timestamp: Date.now()
            });

            // Initialize the supervisor
            const supervisor = new EnhancedIntelligentAutonomousSupervisor2025(enhancedConfig);
            
            // Hook into console logging for progress updates
            const originalConsoleLog = console.log;
            console.log = (...args: any[]) => {
              originalConsoleLog(...args);
              
              const message = args.join(' ');
              
              // Parse autonomous supervisor messages
              if (message.includes('[EnhancedAutonomous2025]')) {
                const cleanMessage = message.replace('[EnhancedAutonomous2025]', '').trim();
                
                // Update progress based on log messages
                if (cleanMessage.includes('🎭 Phase 1: initialization')) {
                  currentPhase = 'planning';
                  progress = 10;
                } else if (cleanMessage.includes('🎭 Phase 2: planning') || cleanMessage.includes('starting research')) {
                  currentPhase = 'research';
                  progress = 25;
                } else if (cleanMessage.includes('🎭 Phase 3: research') || cleanMessage.includes('research complete')) {
                  currentPhase = 'content_generation';
                  progress = 50;
                } else if (cleanMessage.includes('🎭 Phase 4: content') || cleanMessage.includes('writing')) {
                  currentPhase = 'quality_assessment';
                  progress = 75;
                } else if (cleanMessage.includes('🎭 Phase 5: quality') || cleanMessage.includes('quality')) {
                  currentPhase = 'finalization';
                  progress = 90;
                }
                
                // Send progress update
                sendData({
                  type: 'status',
                  phase: currentPhase,
                  message: cleanMessage,
                  progress: progress,
                  timestamp: Date.now()
                });
                
                // Handle completion triggers
                if (cleanMessage.includes('🛑 Maximum iterations reached') || 
                    cleanMessage.includes('⏰ Timeout reached')) {
                  sendData({
                    type: 'status',
                    phase: 'completing',
                    message: 'Execution limit reached - finalizing content generation...',
                    progress: 85,
                    timestamp: Date.now(),
                    note: 'Content generation may continue in background'
                  });
                }
              }
            };

            // Execute autonomous supervisor
            const result = await supervisor.executeAutonomous(sanitizedGoal);
            
            // Restore console.log
            console.log = originalConsoleLog;
            
            const executionTime = Date.now() - startTime;
            
            console.log('✅ Autonomous execution completed');
            console.log(`⏱️ Execution time: ${executionTime}ms`);
            console.log(`🎯 Success: ${result.success}`);
            console.log(`📊 Quality score: ${result.qualityScore || 0}`);
            
            // Intelligent completion detection
            if (result && result.result && result.result.content) {
              // Success case - we have generated content
              sendData({
                type: 'complete',
                result: {
                  title: result.result.title || 'Generated Content',
                  content: result.result.content,
                  metaDescription: result.result.metaDescription || '',
                  wordCount: result.result.wordCount || result.result.content.length / 5,
                  seoScore: result.result.seoScore || 85,
                  readabilityScore: result.result.readabilityScore || 90
                },
                qualityScore: result.qualityScore || 75,
                executionTime,
                message: result.success 
                  ? 'Autonomous execution completed successfully!' 
                  : 'Content generated successfully despite execution limits!',
                success: true,
                timestamp: Date.now()
              });
            } else if (result && result.success === false && result.error) {
              // Genuine error case
              sendData({
                type: 'error',
                error: result.error,
                message: 'Autonomous execution failed',
                executionTime,
                timestamp: Date.now()
              });
            } else {
              // Edge case - result exists but no content
              sendData({
                type: 'error',
                error: 'No content was generated',
                message: 'Autonomous execution completed but no content was produced',
                executionTime,
                timestamp: Date.now()
              });
            }

          } catch (error) {
            console.error('❌ Autonomous streaming execution failed:', error);
            
            sendData({
              type: 'error',
              error: error instanceof Error ? error.message : 'Unknown error',
              message: 'Autonomous execution failed with error',
              timestamp: Date.now()
            });
          } finally {
            // Close the stream
            controller.close();
          }
        })();
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('❌ Stream setup failed:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Stream setup failed',
      code: 'STREAM_SETUP_ERROR'
    }, { status: 500 });
  }
} 