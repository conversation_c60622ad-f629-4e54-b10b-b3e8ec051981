/**
 * Competition Analysis Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for comprehensive competitive analysis including SEO, GEO, and AEO
 */

import { GeminiService } from '../../gemini';
import { AgentState, AgentPhase, CompetitionAgentConfig } from './types';

export class CompetitionAgent {
  private geminiService: GeminiService;
  private config: CompetitionAgentConfig;
  private agentId: string;

  constructor(config: Partial<CompetitionAgentConfig> = {}) {
    this.config = {
      analysisDepth: config.analysisDepth ?? 'comprehensive',
      includeBacklinks: config.includeBacklinks ?? true,
      includeTechnicalSeo: config.includeTechnicalSeo ?? true,
      includeContentGaps: config.includeContentGaps ?? true
    };
    
    this.agentId = 'competition-agent';
    this.geminiService = new GeminiService();
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '🏆 Competition Agent: Starting comprehensive competitive analysis');

    try {
      // Ensure we have research data from previous phase
      if (!state.primaryUrls || !state.researchData) {
        throw new Error('Research data required for competition analysis');
      }

      // Update state to competition analysis phase
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;
      
      // Step 1: Article Type Detection and Analysis
      const articleTypeAnalysis = await this.detectAndAnalyzeArticleType(state);
      this.log(state, '📝 Article type analysis complete');

      // Step 2: SEO Analysis - Traditional ranking factors
      const seoAnalysis = await this.performSeoAnalysis(state);
      this.log(state, '📊 SEO analysis complete');

      // Step 3: GEO Analysis - Geographic and localization factors
      const geoAnalysis = await this.performGeoAnalysis(state);
      this.log(state, '🌍 GEO analysis complete');

      // Step 4: AEO Analysis - Answer Engine Optimization
      const aeoAnalysis = await this.performAeoAnalysis(state);
      this.log(state, '🤖 AEO analysis complete');

      // Step 5: Enhanced Content Gap Analysis with Success Patterns
      const contentGaps = await this.identifyContentGapsAndSuccessPatterns(state);
      this.log(state, '🔍 Enhanced content gap analysis complete');

      // Step 6: Competitor Strengths Analysis (What They Do Right)
      const competitorStrengths = await this.analyzeCompetitorStrengths(state);
      this.log(state, '💪 Competitor strengths analysis complete');

      // Step 7: Ranking Factor Analysis
      const rankingFactors = await this.analyzeRankingFactors(state);
      this.log(state, '📈 Ranking factor analysis complete');

      // Step 8: Writing Pattern Analysis
      const writingPatterns = await this.analyzeWritingPatterns(state);
      this.log(state, '✍️ Writing pattern analysis complete');

      // Compile comprehensive competition analysis
      state.competitorAnalysis = {
        articleTypeAnalysis,
        seoAnalysis,
        geoAnalysis,
        aeoAnalysis,
        contentGaps,
        competitorStrengths,
        rankingFactors,
        writingPatterns
      };

      // Step 7: Create content plan based on all analysis
      const contentPlan = await this.createContentPlan(state);
      state.contentPlan = contentPlan;
      this.log(state, '📋 Content plan created');

      // Mark competition analysis phase as complete
      state.completedPhases.push(AgentPhase.COMPETITION_ANALYSIS);
      state.currentPhase = AgentPhase.CONTENT_PLANNING;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Competition Agent completed in ${executionTime}ms`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown competition analysis error';
      state.errors.push({
        phase: 'competition_analysis',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Competition Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performSeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🔍 Analyzing SEO parameters across competitors');

    const competitorContent = state.primaryUrls!.slice(0, 10); // Top 10 competitors
    
    const seoPrompt = `You are an expert SEO analyst examining competitor content for "${state.topic}".

**Competitor Content Analysis:**
${JSON.stringify(competitorContent.map(c => ({
  url: c.url,
  title: c.title,
  contentLength: c.content.length,
  contentPreview: c.content.substring(0, 1000)
})), null, 2)}

Perform comprehensive SEO analysis focusing on:

1. **Keyword Strategy:**
   - Primary and secondary keywords used
   - Keyword density and placement
   - Long-tail keyword opportunities
   - Semantic keyword variations

2. **Content Structure:**
   - Title tag optimization patterns
   - Header hierarchy usage (H1, H2, H3)
   - Content length distribution
   - Internal linking strategies

3. **Technical SEO Factors:**
   - URL structure patterns
   - Meta description approaches
   - Schema markup usage indicators
   - Content formatting techniques

4. **Ranking Opportunities:**
   - Under-optimized content areas
   - Missing keyword targets
   - Weak content depth areas
   - Technical improvement opportunities

5. **Content Quality Indicators:**
   - Depth of coverage
   - Authority signals
   - Freshness indicators
   - User engagement signals

Return analysis as structured JSON:
{
  "keywordStrategy": {
    "primaryKeywords": [],
    "secondaryKeywords": [],
    "opportunities": []
  },
  "contentStructure": {
    "titlePatterns": [],
    "headerUsage": {},
    "averageLength": 0,
    "linkingStrategies": []
  },
  "technicalFactors": {
    "urlPatterns": [],
    "metaApproaches": [],
    "schemaUsage": [],
    "formatTechniques": []
  },
  "opportunities": {
    "underOptimized": [],
    "missingKeywords": [],
    "weakAreas": [],
    "technicalImprovements": []
  },
  "qualityIndicators": {
    "depthScore": 0,
    "authoritySignals": [],
    "freshnessFactors": [],
    "engagementIndicators": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        seoPrompt,
        { temperature: 0.3, maxOutputTokens: 4096 },
        'SEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ SEO analysis failed: ${error}`);
      return { error: 'SEO analysis failed', rawData: competitorContent.length };
    }
  }

  private async performGeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🌍 Analyzing GEO (Geographic Engine Optimization) factors');

    const geoPrompt = `Analyze geographic and localization optimization factors for "${state.topic}".

**Target Audience:** ${state.targetAudience || 'Global audience'}
**Content Context:** ${state.customInstructions || 'General content'}

Perform GEO analysis focusing on:

1. **Geographic Targeting:**
   - Location-specific optimization opportunities
   - Regional keyword variations
   - Local search optimization potential
   - Geographic content customization needs

2. **Localization Signals:**
   - Language and cultural adaptation requirements
   - Regional terminology preferences
   - Local business integration opportunities
   - Time zone and seasonal considerations

3. **Geographic Content Strategy:**
   - Location-based content gaps
   - Regional case study opportunities
   - Local authority building tactics
   - Geographic link building potential

Return ONLY valid JSON in this exact format:
{
  "geographicTargeting": {
    "primaryRegions": [],
    "keywordVariations": {},
    "localOptimization": [],
    "customizationNeeds": []
  },
  "localizationSignals": {
    "languageAdaptation": [],
    "culturalFactors": [],
    "businessIntegration": [],
    "seasonalConsiderations": []
  },
  "contentStrategy": {
    "locationGaps": [],
    "caseStudyOpportunities": [],
    "authorityTactics": [],
    "linkBuildingPotential": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        geoPrompt,
        { temperature: 0.4, maxOutputTokens: 3072 },
        'GEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ GEO analysis failed: ${error}`);
      return { 
        geographicTargeting: { primaryRegions: [], keywordVariations: {}, localOptimization: [], customizationNeeds: [] },
        localizationSignals: { languageAdaptation: [], culturalFactors: [], businessIntegration: [], seasonalConsiderations: [] },
        contentStrategy: { locationGaps: [], caseStudyOpportunities: [], authorityTactics: [], linkBuildingPotential: [] }
      };
    }
  }

  private async performAeoAnalysis(state: AgentState): Promise<any> {
    this.log(state, '🤖 Analyzing AEO (Answer Engine Optimization) factors');

    const aeoPrompt = `Analyze Answer Engine Optimization factors for "${state.topic}" to optimize for AI search engines like ChatGPT, Perplexity, Claude, and Gemini.

**Research Context:**
- ${state.researchData?.length || 0} research queries analyzed
- ${state.primaryUrls?.length || 0} competitor sources examined
- Target: ${state.contentLength || 2000} word content

Perform AEO analysis focusing on:

1. **AI Search Optimization:**
   - Question-answer pair opportunities
   - Featured snippet optimization potential
   - Voice search optimization factors
   - Conversational query targeting

2. **Structured Data Requirements:**
   - FAQ schema opportunities
   - How-to schema potential
   - Article schema optimization
   - Q&A page structure needs

3. **Answer Format Optimization:**
   - Direct answer structuring
   - Step-by-step format opportunities
   - List-based answer potential
   - Comparison table possibilities

4. **AI Detection Bypass Strategies:**
   - Human writing pattern requirements
   - Natural language flow needs
   - Personality injection opportunities
   - Authenticity markers needed

Return ONLY valid JSON in this exact format:
{
  "aiSearchOptimization": {
    "questionAnswerPairs": [],
    "featuredSnippetOps": [],
    "voiceSearchFactors": [],
    "conversationalQueries": []
  },
  "structuredDataNeeds": {
    "faqSchema": [],
    "howToSchema": [],
    "articleSchema": {},
    "qaPageStructure": []
  },
  "answerFormatting": {
    "directAnswers": [],
    "stepByStepOps": [],
    "listBasedAnswers": [],
    "comparisonTables": []
  },
  "bypassStrategies": {
    "humanPatterns": [],
    "naturalFlow": [],
    "personalityInjection": [],
    "authenticityMarkers": []
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        aeoPrompt,
        { temperature: 0.5, maxOutputTokens: 4096 },
        'AEO Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ AEO analysis failed: ${error}`);
      return { 
        aiSearchOptimization: { questionAnswerPairs: [], featuredSnippetOps: [], voiceSearchFactors: [], conversationalQueries: [] },
        structuredDataNeeds: { faqSchema: [], howToSchema: [], articleSchema: {}, qaPageStructure: [] },
        answerFormatting: { directAnswers: [], stepByStepOps: [], listBasedAnswers: [], comparisonTables: [] },
        bypassStrategies: { humanPatterns: [], naturalFlow: [], personalityInjection: [], authenticityMarkers: [] }
      };
    }
  }

  private async identifyContentGaps(state: AgentState): Promise<string[]> {
    this.log(state, '🔍 Identifying content gaps and opportunities');

    const competitorSample = state.primaryUrls!.slice(0, 5).map(url => ({
      title: url.title,
      contentPreview: url.content.substring(0, 1500)
    }));

    const gapPrompt = `Identify content gaps and opportunities for "${state.topic}".

**Competitor Content Sample:**
${JSON.stringify(competitorSample, null, 2)}

**Target Specifications:**
- Word Count: ${state.contentLength || 2000} words
- Audience: ${state.targetAudience || 'General audience'}
- Tone: ${state.tone || 'Professional'}
- Custom Requirements: ${state.customInstructions || 'None'}

Identify specific content gaps where competitors are weak or missing coverage:

1. **Information Gaps:** What important information is missing?
2. **Depth Gaps:** What topics need deeper coverage?
3. **Practical Gaps:** What actionable advice is missing?
4. **Current Gaps:** What recent developments are not covered?
5. **Unique Perspective Gaps:** What viewpoints are underrepresented?

Return ONLY a JSON array of specific content gaps:
["gap1", "gap2", "gap3", ...]`;

    try {
      const response = await this.geminiService.generateContent(
        gapPrompt,
        { temperature: 0.6, maxOutputTokens: 2048 },
        'Content Gap Analysis'
      );

      const gaps = this.parseJsonResponse(response.response);
      return Array.isArray(gaps) ? gaps : [];
    } catch (error) {
      this.log(state, `⚠️ Content gap analysis failed: ${error}`);
      return [
        'Comprehensive step-by-step guidance',
        'Real-world case studies and examples',
        'Current industry trends and statistics',
        'Common mistakes and how to avoid them',
        'Advanced techniques and strategies'
      ];
    }
  }

  private async analyzeRankingFactors(state: AgentState): Promise<any> {
    this.log(state, '📈 Analyzing ranking factors and optimization opportunities');

    const rankingPrompt = `Analyze ranking factors for "${state.topic}" based on top competitors.

**Competitor Analysis:**
- ${state.primaryUrls?.length || 0} top-ranking competitors analyzed
- ${state.researchData?.length || 0} research data points
- Target content length: ${state.contentLength || 2000} words

Identify key ranking factors and optimization opportunities:

1. **Content Quality Factors:**
   - Depth and comprehensiveness requirements
   - Authority and expertise signals needed
   - Freshness and update frequency
   - User engagement optimization

2. **Technical Optimization:**
   - Site speed and performance factors
   - Mobile optimization requirements
   - Core Web Vitals considerations
   - Schema and structured data needs

3. **Authority Building:**
   - Backlink profile requirements
   - Brand mention optimization
   - Expert citation opportunities
   - Social signal amplification

4. **User Experience Factors:**
   - Content readability optimization
   - Navigation and structure needs
   - Multimedia integration requirements
   - Interaction and engagement features

Return ONLY valid JSON in this exact format:
{
  "contentQuality": {
    "depthRequirements": ["requirement1", "requirement2"],
    "authoritySignals": ["signal1", "signal2"],
    "freshnessFactors": ["factor1", "factor2"],
    "engagementOptimization": ["optimization1", "optimization2"]
  },
  "technicalOptimization": {
    "performanceFactors": ["factor1", "factor2"],
    "mobileRequirements": ["requirement1", "requirement2"],
    "coreWebVitals": ["vital1", "vital2"],
    "structuredData": ["data1", "data2"]
  },
  "authorityBuilding": {
    "backlinkProfile": ["profile1", "profile2"],
    "brandMentions": ["mention1", "mention2"],
    "expertCitations": ["citation1", "citation2"],
    "socialSignals": ["signal1", "signal2"]
  },
  "userExperience": {
    "readabilityOptimization": ["optimization1", "optimization2"],
    "structureNeeds": ["need1", "need2"],
    "multimediaRequirements": ["requirement1", "requirement2"],
    "interactionFeatures": ["feature1", "feature2"]
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        rankingPrompt,
        { temperature: 0.4, maxOutputTokens: 3072 },
        'Ranking Factor Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Ranking factor analysis failed: ${error}`);
      return { 
        contentQuality: { depthRequirements: [], authoritySignals: [], freshnessFactors: [], engagementOptimization: [] },
        technicalOptimization: { performanceFactors: [], mobileRequirements: [], coreWebVitals: [], structuredData: [] },
        authorityBuilding: { backlinkProfile: [], brandMentions: [], expertCitations: [], socialSignals: [] },
        userExperience: { readabilityOptimization: [], structureNeeds: [], multimediaRequirements: [], interactionFeatures: [] }
      };
    }
  }

  private async analyzeWritingPatterns(state: AgentState): Promise<any> {
    this.log(state, '✍️ Analyzing human writing patterns for AI detection bypass');

    const writingPrompt = `Analyze human writing patterns from top-ranking content for "${state.topic}".

**Competitor Content:** ${state.primaryUrls?.length || 0} sources analyzed

Identify human writing characteristics to emulate for bypassing AI detection:

1. **Sentence Structure Patterns:**
   - Sentence length variation
   - Complex vs simple sentence ratios
   - Transition phrase usage
   - Paragraph structure diversity

2. **Vocabulary and Tone:**
   - Industry-specific terminology
   - Conversational elements
   - Personal voice indicators
   - Emotional language usage

3. **Content Flow Patterns:**
   - Introduction/conclusion styles
   - Logical progression methods
   - Tangent and digression usage
   - Question and answer integration

4. **Authenticity Markers:**
   - Personal experience references
   - Opinion and perspective sharing
   - Uncertainty expressions
   - Casual language elements

Return ONLY valid JSON in this exact format:
{
  "sentencePatterns": {
    "averageLength": 15,
    "variationRange": "8-25",
    "complexToSimpleRatio": "40:60",
    "commonTransitions": ["however", "meanwhile", "additionally"]
  },
  "vocabularyTone": {
    "industryTerms": ["term1", "term2"],
    "conversationalElements": ["element1", "element2"],
    "personalVoiceIndicators": ["indicator1", "indicator2"]
  },
  "contentFlow": {
    "introStyle": "description",
    "conclusionStyle": "description",
    "progressionMethod": "description"
  },
  "authenticityMarkers": {
    "personalReferences": ["marker1", "marker2"],
    "uncertaintyExpressions": ["maybe", "perhaps"],
    "casualLanguage": ["honestly", "basically"]
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        writingPrompt,
        { temperature: 0.5, maxOutputTokens: 3072 },
        'Writing Pattern Analysis'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Writing pattern analysis failed: ${error}`);
      return { 
        sentencePatterns: { averageLength: 15, variationRange: "8-25", complexToSimpleRatio: "40:60", commonTransitions: [] },
        vocabularyTone: { industryTerms: [], conversationalElements: [], personalVoiceIndicators: [] },
        contentFlow: { introStyle: "direct", conclusionStyle: "summary", progressionMethod: "logical" },
        authenticityMarkers: { personalReferences: [], uncertaintyExpressions: [], casualLanguage: [] }
      };
    }
  }

  private async createContentPlan(state: AgentState): Promise<any> {
    this.log(state, '📋 Creating content plan based on competition analysis');

    const planPrompt = `Create a comprehensive content plan for "${state.topic}" based on extensive competition analysis.

**Analysis Results:**
- Content Gaps: ${JSON.stringify(state.competitorAnalysis?.contentGaps, null, 2)}
- SEO Analysis: ${state.competitorAnalysis?.seoAnalysis ? 'Complete' : 'Fallback'}
- Ranking Factors: ${state.competitorAnalysis?.rankingFactors ? 'Available' : 'Basic'}
- Writing Patterns: ${state.competitorAnalysis?.writingPatterns ? 'Available' : 'Basic'}

**Target Specifications:**
- Word Count: ${state.contentLength || 2000} words
- Audience: ${state.targetAudience || 'General audience'}
- Tone: ${state.tone || 'Professional'}
- Content Type: ${state.contentType || 'article'}

Return ONLY valid JSON in this exact format:
{
  "articleType": "comprehensive guide",
  "structure": [
    "introduction",
    "main_sections",
    "conclusion"
  ],
  "keyPoints": [
    "Point 1",
    "Point 2",
    "Point 3"
  ],
  "targetWordCount": ${state.contentLength || 2000},
  "seoStrategy": {
    "primaryKeywords": [],
    "secondaryKeywords": [],
    "headerStrategy": [],
    "metaApproach": ""
  }
}`;

    try {
      const response = await this.geminiService.generateContent(
        planPrompt,
        { temperature: 0.4, maxOutputTokens: 2048 },
        'Content Plan Creation'
      );

      return this.parseJsonResponse(response.response);
    } catch (error) {
      this.log(state, `⚠️ Content plan creation failed: ${error}`);
      return {
        articleType: 'comprehensive guide',
        structure: ['introduction', 'main_content', 'practical_examples', 'conclusion'],
        keyPoints: state.competitorAnalysis?.contentGaps || [
          'Comprehensive overview of the topic',
          'Step-by-step guidance',
          'Real-world examples',
          'Best practices and tips'
        ],
        targetWordCount: state.contentLength || 2000,
        seoStrategy: {
          primaryKeywords: state.keywords || [],
          secondaryKeywords: [],
          headerStrategy: ['H1 for title', 'H2 for main sections', 'H3 for subsections'],
          metaApproach: 'Focus on user intent and search queries'
        }
      };
    }
  }

  private parseJsonResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Enhanced conversational text detection - handle more patterns
      const conversationalStarters = [
        'Here\'s', 'Here is', 'Here are', 'Based on', 'Okay,', 'Sure,', 
        'Certainly,', 'Of course,', 'Let me', 'I\'ll', 'I will',
        'Since', 'Given', 'Considering', 'Taking into account',
        'After analyzing', 'Upon review', 'Looking at'
      ];
      
      const startsWithConversational = conversationalStarters.some(starter => 
        cleanResponse.startsWith(starter)
      );
      
      if (startsWithConversational) {
        // Look for JSON object in the response - more comprehensive search
        const jsonMatches = [
          cleanResponse.match(/\{[\s\S]*\}/), // Basic JSON object match
          cleanResponse.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g), // Nested objects
        ];
        
        for (const match of jsonMatches) {
          if (match) {
            const jsonCandidate = Array.isArray(match) ? match[match.length - 1] : match[0];
            try {
              // Try to parse this candidate
              const parsed = JSON.parse(jsonCandidate);
              cleanResponse = jsonCandidate;
              break;
            } catch (parseError) {
              // Continue to next candidate
              continue;
            }
          }
        }
      }
      
      // Additional cleanup for common JSON formatting issues
      cleanResponse = cleanResponse
        .replace(/,\s*}/g, '}') // Remove trailing commas in objects
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3') // Quote unquoted keys
        .trim();
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse);
    } catch (error) {
      // Enhanced fallback - try to extract any valid JSON from the text
      try {
        console.warn('Primary JSON parsing failed, trying extraction fallback');
        
        // Look for any JSON-like structures in the response
        const jsonCandidates = response.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
        
        if (jsonCandidates && jsonCandidates.length > 0) {
          // Try each candidate until one parses successfully
          for (const candidate of jsonCandidates) {
            try {
              const parsed = JSON.parse(candidate);
              console.log('✅ Successfully extracted JSON from conversational response');
              return parsed;
            } catch (candidateError) {
              continue;
            }
          }
        }
      } catch (extractionError) {
        console.warn('JSON extraction fallback also failed');
      }
      
      // If all parsing fails, return a structured fallback
      console.warn('Failed to parse JSON response, using fallback:', error);
      console.warn('Response preview:', response.substring(0, 200) + '...');
      
      return {
        error: 'Failed to parse response',
        rawResponse: response.substring(0, 500) + '...',
        fallbackUsed: true
      };
    }
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  /**
   * Detect and analyze article type based on topic and competitor content
   */
  private async detectAndAnalyzeArticleType(state: AgentState): Promise<any> {
    this.log(state, '📝 Detecting optimal article type for topic');

    const prompt = `
ARTICLE TYPE INTELLIGENCE SYSTEM - 2025 ANALYSIS

Analyze the topic "${state.topic}" and determine the optimal article type and structure.

ARTICLE TYPES TO CONSIDER:
1. LISTICLES - Numbered lists, engaging hooks, data-driven points
   - Best for: "Top X", "Best", "Ways to", comparison topics
   - Structure: Hook → List items with explanations → Conclusion
   - Engagement: High shareability, scannable format

2. HOW-TO GUIDES - Step-by-step tutorials, actionable content
   - Best for: Process explanations, tutorials, instructional content
   - Structure: Problem → Solution steps → Results/Tips
   - Engagement: High utility value, bookmark-worthy

3. PRODUCT REVIEWS - Comparison-based, feature analysis
   - Best for: Product comparisons, service evaluations
   - Structure: Overview → Features → Pros/Cons → Verdict
   - Engagement: Purchase decision support

4. NEWS ARTICLES - Timely, fact-based, inverted pyramid
   - Best for: Current events, announcements, updates
   - Structure: Lead → Body → Background → Conclusion
   - Engagement: Timeliness and relevance

5. OPINION PIECES - Argument-driven, personal perspective
   - Best for: Controversial topics, thought leadership
   - Structure: Thesis → Arguments → Counter-arguments → Conclusion
   - Engagement: Debate and discussion

6. CASE STUDIES - Problem-solution narrative, data-driven
   - Best for: Success stories, business examples
   - Structure: Challenge → Solution → Results → Lessons
   - Engagement: Credibility and proof

7. RESEARCH ARTICLES - Data-heavy, citation-rich, analytical
   - Best for: Industry insights, trend analysis
   - Structure: Abstract → Methodology → Findings → Implications
   - Engagement: Authority and expertise

ANALYSIS REQUIRED:
1. Primary article type recommendation
2. Secondary article type (if hybrid approach needed)
3. Key structural elements for this type
4. Engagement optimization strategies
5. Content length recommendations
6. Visual elements needed (tables, charts, images)
7. SEO considerations for this article type

TOPIC CONTEXT:
- Topic: "${state.topic}"
- Target audience: ${state.targetAudience || 'General audience'}
- Research data available: ${state.researchData ? 'Yes' : 'No'}

Provide detailed analysis in JSON format:
{
  "primaryType": "article_type_name",
  "confidence": 0.95,
  "secondaryType": "optional_secondary_type",
  "reasoning": "why this type is optimal",
  "structure": {
    "sections": ["section1", "section2", "section3"],
    "wordCountRange": [min, max],
    "keyElements": ["element1", "element2"]
  },
  "engagementStrategies": ["strategy1", "strategy2"],
  "seoConsiderations": ["consideration1", "consideration2"],
  "visualElements": ["tables", "charts", "images"],
  "competitorAnalysisNeeded": ["what to analyze in competitors"]
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const analysis = this.parseJsonResponse(response.response);

      this.log(state, `✅ Detected article type: ${analysis.primaryType} (${analysis.confidence * 100}% confidence)`);

      return analysis;
    } catch (error) {
      this.log(state, `❌ Article type detection failed: ${error}`);
      return {
        primaryType: 'how-to-guide',
        confidence: 0.5,
        reasoning: 'Default fallback due to analysis error',
        structure: { sections: ['introduction', 'main-content', 'conclusion'], wordCountRange: [1500, 2500] }
      };
    }
  }

  /**
   * Enhanced content gap analysis that identifies what competitors do right
   */
  private async identifyContentGapsAndSuccessPatterns(state: AgentState): Promise<any> {
    this.log(state, '🔍 Analyzing content gaps AND success patterns');

    const competitorSample = state.primaryUrls!.slice(0, 5);

    const prompt = `
ENHANCED COMPETITIVE CONTENT ANALYSIS - 2025 METHODOLOGY

Analyze competitor content for topic "${state.topic}" to identify:
1. CONTENT GAPS (what's missing)
2. SUCCESS PATTERNS (what they do right)
3. IMPROVEMENT OPPORTUNITIES (how to do it better)

COMPETITOR CONTENT SAMPLE:
${competitorSample.map((comp, i) => `
COMPETITOR ${i + 1}: ${comp.url}
CONTENT PREVIEW: ${comp.content.substring(0, 1000)}...
`).join('\n')}

ANALYSIS FRAMEWORK:

A. CONTENT GAPS ANALYSIS:
- Topics not covered by any competitor
- Depth gaps (surface-level vs deep coverage)
- Format gaps (missing content types)
- Audience gaps (underserved segments)
- Timeliness gaps (outdated information)

B. SUCCESS PATTERN RECOGNITION:
- Common structural elements across top performers
- Engagement tactics that work
- Content depth and quality indicators
- Visual and formatting strategies
- Call-to-action patterns
- Data usage and citation practices

C. IMPROVEMENT OPPORTUNITIES:
- How to combine best practices from multiple competitors
- Ways to exceed current quality standards
- Unique angles not being explored
- Better organization and presentation methods
- Enhanced user experience elements

D. COMPETITIVE INTELLIGENCE:
- Word count analysis of top performers
- Heading structure patterns
- Use of data, statistics, examples
- Visual content integration
- Internal/external linking strategies

Provide comprehensive analysis in JSON format:
{
  "contentGaps": {
    "topicGaps": ["gap1", "gap2"],
    "depthGaps": ["area1", "area2"],
    "formatGaps": ["format1", "format2"],
    "audienceGaps": ["segment1", "segment2"]
  },
  "successPatterns": {
    "structuralElements": ["element1", "element2"],
    "engagementTactics": ["tactic1", "tactic2"],
    "qualityIndicators": ["indicator1", "indicator2"],
    "visualStrategies": ["strategy1", "strategy2"]
  },
  "improvementOpportunities": {
    "qualityEnhancements": ["enhancement1", "enhancement2"],
    "uniqueAngles": ["angle1", "angle2"],
    "betterOrganization": ["method1", "method2"],
    "userExperience": ["improvement1", "improvement2"]
  },
  "competitiveIntelligence": {
    "averageWordCount": 2500,
    "commonHeadingStructure": ["h1", "h2", "h3"],
    "dataUsagePatterns": ["pattern1", "pattern2"],
    "linkingStrategies": ["strategy1", "strategy2"]
  },
  "actionableInsights": ["insight1", "insight2", "insight3"]
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const analysis = this.parseJsonResponse(response.response);

      this.log(state, `✅ Identified ${analysis.contentGaps?.topicGaps?.length || 0} content gaps and ${analysis.successPatterns?.structuralElements?.length || 0} success patterns`);

      return analysis;
    } catch (error) {
      this.log(state, `❌ Enhanced content gap analysis failed: ${error}`);
      return {
        contentGaps: { topicGaps: [], depthGaps: [], formatGaps: [], audienceGaps: [] },
        successPatterns: { structuralElements: [], engagementTactics: [], qualityIndicators: [], visualStrategies: [] },
        improvementOpportunities: { qualityEnhancements: [], uniqueAngles: [], betterOrganization: [], userExperience: [] },
        competitiveIntelligence: { averageWordCount: 2000, commonHeadingStructure: [], dataUsagePatterns: [], linkingStrategies: [] },
        actionableInsights: []
      };
    }
  }

  /**
   * Analyze what competitors do right to learn from their success
   */
  private async analyzeCompetitorStrengths(state: AgentState): Promise<any> {
    this.log(state, '💪 Analyzing competitor strengths and success factors');

    const competitorSample = state.primaryUrls!.slice(0, 5);

    const prompt = `
COMPETITOR STRENGTHS ANALYSIS - 2025 SUCCESS PATTERN RECOGNITION

Analyze what competitors do RIGHT for topic "${state.topic}" to understand success factors.

ANALYSIS FOCUS:
1. CONTENT QUALITY INDICATORS
2. ENGAGEMENT OPTIMIZATION
3. USER EXPERIENCE EXCELLENCE
4. SEO BEST PRACTICES
5. STRUCTURAL INNOVATIONS

COMPETITOR CONTENT:
${competitorSample.map((comp, i) => `${i + 1}. ${comp.title || 'Untitled'} (${comp.url || 'No URL'})
Content: ${comp.content ? comp.content.substring(0, 500) + '...' : 'No content available'}`).join('\n\n')}

STRENGTH ANALYSIS FRAMEWORK:

A. CONTENT EXCELLENCE:
- Depth and comprehensiveness
- Accuracy and credibility
- Unique insights and perspectives
- Data integration and citations
- Practical value and actionability

B. ENGAGEMENT MASTERY:
- Hook and introduction effectiveness
- Storytelling and narrative flow
- Visual content integration
- Interactive elements
- Call-to-action optimization

C. USER EXPERIENCE SUPERIORITY:
- Content organization and structure
- Readability and scannability
- Mobile optimization
- Loading speed considerations
- Navigation and internal linking

D. SEO OPTIMIZATION EXCELLENCE:
- Keyword integration naturalness
- Meta optimization
- Schema markup usage
- Technical SEO implementation
- Content freshness and updates

E. INNOVATION FACTORS:
- Unique content formats
- Creative presentation methods
- Technology integration
- Community engagement features
- Personalization elements

Provide detailed analysis in JSON format:
{
  "contentExcellence": {
    "depthFactors": ["factor1", "factor2"],
    "credibilitySignals": ["signal1", "signal2"],
    "uniqueInsights": ["insight1", "insight2"],
    "practicalValue": ["value1", "value2"]
  },
  "engagementMastery": {
    "hookStrategies": ["strategy1", "strategy2"],
    "narrativeFlow": ["element1", "element2"],
    "visualIntegration": ["method1", "method2"],
    "ctaOptimization": ["technique1", "technique2"]
  },
  "userExperienceSuperiority": {
    "organizationMethods": ["method1", "method2"],
    "readabilityFactors": ["factor1", "factor2"],
    "mobileOptimization": ["feature1", "feature2"],
    "navigationExcellence": ["element1", "element2"]
  },
  "seoOptimizationExcellence": {
    "keywordIntegration": ["technique1", "technique2"],
    "metaOptimization": ["method1", "method2"],
    "technicalSeo": ["factor1", "factor2"],
    "contentFreshness": ["strategy1", "strategy2"]
  },
  "innovationFactors": {
    "uniqueFormats": ["format1", "format2"],
    "presentationMethods": ["method1", "method2"],
    "technologyIntegration": ["tech1", "tech2"],
    "engagementFeatures": ["feature1", "feature2"]
  },
  "keySuccessFactors": ["factor1", "factor2", "factor3"],
  "implementationPriority": ["high_priority1", "medium_priority1", "low_priority1"]
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const analysis = this.parseJsonResponse(response.response);

      this.log(state, `✅ Analyzed competitor strengths: ${analysis.keySuccessFactors?.length || 0} key success factors identified`);

      return analysis;
    } catch (error) {
      this.log(state, `❌ Competitor strengths analysis failed: ${error}`);
      return {
        contentExcellence: { depthFactors: [], credibilitySignals: [], uniqueInsights: [], practicalValue: [] },
        engagementMastery: { hookStrategies: [], narrativeFlow: [], visualIntegration: [], ctaOptimization: [] },
        userExperienceSuperiority: { organizationMethods: [], readabilityFactors: [], mobileOptimization: [], navigationExcellence: [] },
        seoOptimizationExcellence: { keywordIntegration: [], metaOptimization: [], technicalSeo: [], contentFreshness: [] },
        innovationFactors: { uniqueFormats: [], presentationMethods: [], technologyIntegration: [], engagementFeatures: [] },
        keySuccessFactors: [],
        implementationPriority: []
      };
    }
  }

  getCapabilities() {
    return {
      name: 'Competition Analysis Agent',
      description: 'Comprehensive SEO, GEO, and AEO competitive analysis specialist with 2025 article intelligence',
      inputTypes: ['primaryUrls', 'researchData', 'topic', 'targetAudience'],
      outputTypes: ['competitorAnalysis', 'seoAnalysis', 'geoAnalysis', 'aeoAnalysis', 'articleTypeAnalysis'],
      dependencies: ['gemini-service'],
      parallel: false
    };
  }

  getMetrics(state: AgentState) {
    const competitionLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: competitionLogs.length > 0 ? Date.now() - competitionLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'competition_analysis').length === 0 ? 100 : 0,
      qualityScore: state.competitorAnalysis ? 85 : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'competition_analysis').length,
      lastExecution: Date.now()
    };
  }
} 