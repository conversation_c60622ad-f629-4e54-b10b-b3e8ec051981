/**
 * Invincible v.2 Multi-Agent System
 * Main entry point for autonomous content generation
 */

export { InvincibleOrchestrator } from './orchestrator';
export { AdvancedDeepResearchAgent } from './research-agent';
export { CompetitionAgent } from './competition-agent';
export { WritingAgent } from './writing-agent';
export { QualityAgent } from './quality-agent';

// Import for internal use
import { InvincibleOrchestrator } from './orchestrator';
import { MultiAgentConfig } from './types';

export type {
  AgentState,
  AgentPhase,
  MultiAgentConfig,
  MultiAgentResult,
  WorkflowStep,
  ResearchAgentConfig,
  CompetitionAgentConfig,
  WritingAgentConfig,
  QualityAgentConfig,
  AgentCapability,
  AgentMetrics
} from './types';

// Default configuration for easy setup
export const DEFAULT_CONFIG = {
  research: {
    searchDepth: 3,
    maxUrls: 10,
    parallelSearches: 3,
    researchQueries: 5
  },
  competition: {
    analysisDepth: 'comprehensive' as const,
    includeBacklinks: true,
    includeTechnicalSeo: true,
    includeContentGaps: true
  },
  writing: {
    humanizationLevel: 'maximum' as const,
    creativityLevel: 0.8,
    seoOptimization: true,
    externalLinking: true,
    tableGeneration: true
  },
  quality: {
    aiDetectionCheck: true,
    grammarCheck: true,
    factCheck: true,
    seoValidation: true,
    readabilityCheck: true,
    qualityThreshold: 85
  },
  maxExecutionTime: 900000, // 15 minutes
  enableParallelProcessing: true,
  enableAdaptiveLearning: true,
  qualityThreshold: 85,
  enableHumanLoop: false
};

// Quick setup function
export function createInvincibleV2(config?: Partial<MultiAgentConfig>): InvincibleOrchestrator {
  return new InvincibleOrchestrator(config);
}

// Export version info
export const VERSION = '2.0.0';
export const DESCRIPTION = 'Autonomous multi-agent content generation system with superior quality and AI detection bypass';
export const FEATURES = [
  'Multi-agent architecture with specialized roles',
  'Advanced AI detection bypass techniques',
  'SEO/GEO/AEO optimization',
  'Quality assurance and validation',
  'Real-time execution monitoring',
  'Autonomous content generation'
]; 