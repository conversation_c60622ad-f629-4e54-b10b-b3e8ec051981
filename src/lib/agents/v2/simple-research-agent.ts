/**
 * Simple Research Agent - Query-Based Research System
 * 
 * Executes research queries provided by the supervisor without complex processing.
 * No knowledge graphs, mind maps, or complex analysis - just clean query execution.
 */

import { TavilySearchService } from '../../search';
import { NodeWebScraperService } from '../../web-scraper';
import { AgentState, AgentPhase } from './types';

interface SimpleResearchConfig {
  maxUrls: number;
  parallelSearches: number;
  enableWebScraping: boolean;
}

export class ResearchAgent {
  private config: SimpleResearchConfig;
  private searchService: TavilySearchService;
  private webScraperService: NodeWebScraperService;

  constructor(config: Partial<SimpleResearchConfig> = {}) {
    this.config = {
      maxUrls: config.maxUrls ?? 10,
      parallelSearches: config.parallelSearches ?? 3,
      enableWebScraping: config.enableWebScraping ?? true,
      ...config
    };

    this.searchService = new TavilySearchService();
    this.webScraperService = new NodeWebScraperService();
  }

  /**
   * Execute simple research based on provided queries
   */
  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    this.log(state, '🔍 Simple Research Agent: Starting query-based research');

    try {
      state.currentPhase = AgentPhase.RESEARCH;

      // Use supervisor-provided queries or fallback to topic
      const queries = state.researchQueries || [state.topic];
      this.log(state, `📝 Executing ${queries.length} research queries`);

      // Execute all queries in parallel
      const allResults = await this.executeQueriesInParallel(queries, state);
      
      // Extract URLs for web scraping
      const urlsToScrape = this.extractTopUrls(allResults);
      
      // Scrape URLs if enabled
      let scrapedContent: Array<{url: string, content: string, title: string}> = [];
      if (this.config.enableWebScraping && urlsToScrape.length > 0) {
        scrapedContent = await this.scrapeUrls(urlsToScrape, state);
      }

      // Store results in state
      state.researchData = allResults;
      state.primaryUrls = scrapedContent;

      // Mark research phase as complete
      state.completedPhases.push(AgentPhase.RESEARCH);
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;

      const executionTime = Date.now() - startTime;
      this.log(state, `✅ Simple Research completed in ${executionTime}ms`);
      this.log(state, `📊 Results: ${allResults.length} queries, ${scrapedContent.length} scraped URLs`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown research error';
      state.errors.push({
        phase: 'research',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Simple Research failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  /**
   * Execute multiple queries in parallel
   */
  private async executeQueriesInParallel(queries: string[], state: AgentState): Promise<any[]> {
    const batchSize = this.config.parallelSearches;
    const allResults: any[] = [];

    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      this.log(state, `🔎 Processing query batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(queries.length / batchSize)}`);

      const batchPromises = batch.map(async (query, index) => {
        try {
          const searchResults = await this.searchService.search(query, 5);
          return {
            query,
            results: searchResults.items || [],
            source: 'tavily',
            success: true,
            timestamp: Date.now()
          };
        } catch (error) {
          this.log(state, `⚠️ Query failed: "${query}"`);
          return {
            query,
            results: [],
            source: 'tavily',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now()
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      allResults.push(...batchResults);

      // Small delay between batches to be polite
      if (i + batchSize < queries.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return allResults;
  }

  /**
   * Extract top URLs from search results
   */
  private extractTopUrls(results: any[]): string[] {
    const urls: string[] = [];
    
    results.forEach(result => {
      if (result.success && result.results) {
        result.results.slice(0, 2).forEach((item: any) => {
          const url = item.url || item.link;
          if (url && !urls.includes(url)) {
            urls.push(url);
          }
        });
      }
    });

    return urls.slice(0, this.config.maxUrls);
  }

  /**
   * Scrape URLs for content
   */
  private async scrapeUrls(urls: string[], state: AgentState): Promise<Array<{url: string, content: string, title: string}>> {
    this.log(state, `🕷️ Scraping ${urls.length} URLs`);
    
    try {
      const scrapedResults = await this.webScraperService.scrapeMultipleUrls(urls);
      
      const successfulScrapes = scrapedResults
        .filter(result => result.success && result.content)
        .map(result => ({
          url: result.url,
          content: result.content,
          title: result.title || `Content from ${new URL(result.url).hostname}`
        }));

      this.log(state, `✅ Successfully scraped ${successfulScrapes.length}/${urls.length} URLs`);
      return successfulScrapes;

    } catch (error) {
      this.log(state, `⚠️ Web scraping failed: ${error}`);
      return [];
    }
  }

  /**
   * Simple logging helper
   */
  private log(state: AgentState, message: string): void {
    console.log(`[simple-research-agent] ${message}`);
    state.logs.push({
      agent: 'research',
      message,
      timestamp: Date.now()
    });
  }
} 