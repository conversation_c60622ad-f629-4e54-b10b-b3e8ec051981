/**
 * Enhanced Intelligent Autonomous Supervisor 2025
 * 
 * Revolutionary research pipeline that conducts deep competitive intelligence:
 * - Quick Tavily search for exact topic
 * - Parallel web scraping of 10 URLs  
 * - Three-stage analysis: topic understanding, data extraction, query generation
 * - Intelligent planning based on comprehensive analysis
 * - Works for all article types (alternatives, guides, tutorials, listicles, etc.)
 */

import { GeminiService } from '../../gemini';
import { TavilySearchService } from '../../search';
import { NodeWebScraperService } from '../../web-scraper';
import { ResearchAgent } from '../v2/simple-research-agent';
import { CompetitionAgent } from '../v2/competition-agent';
import { WritingAgent } from '../v2/writing-agent';
import { QualityAgent } from '../v2/quality-agent';
import { AgentState, AgentPhase } from '../v2/types';

// ============================================================================
// ENHANCED INTELLIGENCE INTERFACES
// ============================================================================

export interface ScrapedPageData {
  url: string;
  title: string;
  content: string;
  wordCount: number;
  extractedData: {
    // Stage 1: Topic Understanding
    mainTopic: string;
    articleType: string;
    targetAudience: string;
    
    // Stage 2: Data Points
    keyStatistics: string[];
    importantFacts: string[];
    trends: string[];
    
    // Stage 3: Query Generation Insights
    topicCoverage: string[];
    missingElements: string[];
    competitorFocus: string[];
  };
}

export interface IntelligenceAnalysis {
  // Stage 1: Topic Context Understanding
  topicAnalysis: {
    primaryTopic: string;
    topicVariations: string[];
    articleTypeDetected: string;
    targetAudienceProfile: string;
    contentComplexity: 'beginner' | 'intermediate' | 'advanced';
    purposeIdentified: string;
  };
  
  // Stage 2: Data & Statistics Extraction  
  dataAnalysis: {
    keyStatistics: Array<{
      statistic: string;
      source: string;
      importance: number;
    }>;
    importantFacts: Array<{
      fact: string;
      category: string;
      sources: string[];
    }>;
    trends: Array<{
      trend: string;
      timeframe: string;
      significance: string;
    }>;
    commonDataPoints: string[];
  };
  
  // Stage 3: Query Generation Intelligence
  queryIntelligence: {
    // For alternatives/comparisons
    alternatives?: {
      mentionedAlternatives: string[];
      keyComparisonPoints: string[];
      evaluationCriteria: string[];
      missingAlternatives: string[];
    };
    
    // For guides/tutorials  
    guidance?: {
      stepCategories: string[];
      commonChallenges: string[];
      toolsNeeded: string[];
      skillLevels: string[];
    };
    
    // For informational content
    information?: {
      keyTopics: string[];
      subtopicAreas: string[];
      expertiseAreas: string[];
      knowledgeGaps: string[];
    };
    
    // Universal query generation data
    universalInsights: {
      commonQuestions: string[];
      informationGaps: string[];
      deeperTopics: string[];
      relatedConcepts: string[];
    };
  };
}

export interface IntelligentPlan {
  // Enhanced plan based on intelligence analysis
  title: string;
  approach: string;
  articleType: string;
  
  // Intelligent research queries based on analysis
  intelligentQueries: Array<{
    query: string;
    purpose: string;
    priority: 'high' | 'medium' | 'low';
    expectedFindings: string[];
  }>;
  
  // Content strategy based on competitor analysis
  contentStrategy: {
    keyPoints: string[];
    requiredSections: string[];
    dataToInclude: string[];
    uniqueAngle: string;
  };
  
  // Execution parameters
  targetWordCount: number;
  estimatedComplexity: number;
  requiredExpertise: string[];
}

export interface EnhancedAutonomousState {
  // Core execution
  goal: string;
  currentPhase: string;
  
  // Intelligence pipeline data
  initialSearchResults: any[];
  scrapedPages: ScrapedPageData[];
  intelligenceAnalysis: IntelligenceAnalysis | null;
  intelligentPlan: IntelligentPlan | null;
  
  // Traditional workflow data
  research: any;
  competition: any;
  content: any;
  qualityScore: number;
  
  // Execution tracking
  errors: string[];
  logs: string[];
  retryCount: number;
  isComplete: boolean;
  finalResult: any;
  metadata: Record<string, any>;
}

// ============================================================================
// ENHANCED INTELLIGENT AUTONOMOUS SUPERVISOR
// ============================================================================

export class EnhancedIntelligentAutonomousSupervisor2025 {
  private geminiService: GeminiService;
  private searchService: TavilySearchService;
  private webScraperService: NodeWebScraperService;
  private config: any;
  
  // Traditional agents for later phases
  private researchAgent: ResearchAgent;
  private competitionAgent: CompetitionAgent;
  private writingAgent: WritingAgent;
  private qualityAgent: QualityAgent;

  constructor(config: any = {}) {
    this.config = {
      maxRetries: 3,
      qualityThreshold: 85,
      timeoutMinutes: 20,
      parallelScrapingCount: 10,
      ...config
    };

    // Initialize services
    this.geminiService = new GeminiService();
    this.searchService = new TavilySearchService();
    this.webScraperService = new NodeWebScraperService();
    
    // Initialize traditional agents
    this.researchAgent = new ResearchAgent();
    this.competitionAgent = new CompetitionAgent();
    this.writingAgent = new WritingAgent();
    this.qualityAgent = new QualityAgent();
  }

  /**
   * MAIN EXECUTION: Revolutionary Intelligence Pipeline
   */
  async executeAutonomous(goal: string): Promise<any> {
    try {
      console.log(`🧠 Enhanced Intelligent Supervisor 2025: Starting revolutionary research pipeline`);
      console.log(`🎯 Goal: "${goal}"`);

      // Initialize enhanced state
      const state: EnhancedAutonomousState = {
        goal,
        currentPhase: "intelligence_pipeline",
        initialSearchResults: [],
        scrapedPages: [],
        intelligenceAnalysis: null,
        intelligentPlan: null,
        research: {},
        competition: {},
        content: {},
        qualityScore: 0,
        errors: [],
        logs: [],
        retryCount: 0,
        isComplete: false,
        finalResult: null,
        metadata: { startTime: Date.now() }
      };

      // ========================================================================
      // PHASE 1: QUICK TAVILY SEARCH FOR EXACT TOPIC
      // ========================================================================
      await this.performQuickTopicSearch(state);

      // ========================================================================
      // PHASE 2: PARALLEL WEB SCRAPING OF 10 URLS
      // ========================================================================
      await this.performParallelWebScraping(state);

      // ========================================================================
      // PHASE 3: THREE-STAGE ANALYSIS PIPELINE
      // ========================================================================
      await this.performThreeStageAnalysis(state);

      // ========================================================================
      // PHASE 4: INTELLIGENT PLANNING BASED ON ANALYSIS
      // ========================================================================
      await this.createIntelligentPlan(state);

      // ========================================================================
      // PHASE 5: EXECUTE TRADITIONAL WORKFLOW WITH INTELLIGENCE
      // ========================================================================
      await this.executeIntelligentWorkflow(state);

      return {
        success: true,
        result: state.finalResult,
        qualityScore: state.qualityScore,
        executionTime: Date.now() - state.metadata.startTime,
        intelligenceInsights: {
          scrapedPagesCount: state.scrapedPages.length,
          analysisCompleted: !!state.intelligenceAnalysis,
          intelligentQueriesGenerated: state.intelligentPlan?.intelligentQueries.length || 0,
          finalQuality: state.qualityScore
        }
      };

    } catch (error) {
      console.error('🚨 Enhanced Intelligence Pipeline failed:', error);
      throw error;
    }
  }

  /**
   * PHASE 1: Quick Tavily Search for Exact Topic
   */
  private async performQuickTopicSearch(state: EnhancedAutonomousState): Promise<void> {
    console.log('🔍 PHASE 1: Quick Tavily search for exact topic');
    state.currentPhase = "quick_search";

    try {
      // Search for exact topic with current context
      const searchResults = await this.searchService.search(state.goal, 10);
      state.initialSearchResults = searchResults.items || [];
      
      console.log(`✅ Quick search complete: ${state.initialSearchResults.length} results found`);
      state.logs.push(`Quick search found ${state.initialSearchResults.length} initial results`);

    } catch (error) {
      console.error('❌ Quick search failed:', error);
      state.errors.push(`Quick search failed: ${error}`);
      throw error;
    }
  }

  /**
   * PHASE 2: Parallel Web Scraping of 10 URLs
   */
  private async performParallelWebScraping(state: EnhancedAutonomousState): Promise<void> {
    console.log('🕷️ PHASE 2: Parallel web scraping of top URLs');
    state.currentPhase = "parallel_scraping";

    try {
      // Extract top 10 URLs
      const urls = state.initialSearchResults
        .slice(0, this.config.parallelScrapingCount)
        .map(result => result.url)
        .filter(url => url);

      console.log(`🌐 Starting parallel scraping of ${urls.length} URLs`);

      // Parallel scraping with enhanced data extraction
      const scrapingPromises = urls.map(async (url, index) => {
        try {
          const scrapedContent = await this.webScraperService.scrapeUrl(url);
          
          if (scrapedContent && scrapedContent.success && scrapedContent.content) {
            const pageData: ScrapedPageData = {
              url: scrapedContent.url,
              title: scrapedContent.title || `Page ${index + 1}`,
              content: scrapedContent.content,
              wordCount: scrapedContent.wordCount || 0,
              extractedData: {
                mainTopic: '',
                articleType: '',
                targetAudience: '',
                keyStatistics: [],
                importantFacts: [],
                trends: [],
                topicCoverage: [],
                missingElements: [],
                competitorFocus: []
              }
            };

            console.log(`✅ Scraped: ${new URL(url).hostname} (${pageData.wordCount} words)`);
            return pageData;
          } else {
            console.log(`⚠️ Failed to scrape: ${url}`);
            return null;
          }
        } catch (scrapeError) {
          console.log(`⚠️ Scraping error for ${url}: ${scrapeError}`);
          return null;
        }
      });

      // Wait for all scraping to complete
      const scrapingResults = await Promise.all(scrapingPromises);
      state.scrapedPages = scrapingResults.filter(page => page !== null) as ScrapedPageData[];

      console.log(`✅ Parallel scraping complete: ${state.scrapedPages.length}/${urls.length} pages successfully scraped`);
      state.logs.push(`Parallel scraping: ${state.scrapedPages.length} pages scraped successfully`);

    } catch (error) {
      console.error('❌ Parallel scraping failed:', error);
      state.errors.push(`Parallel scraping failed: ${error}`);
      throw error;
    }
  }

  /**
   * PHASE 3: Three-Stage Analysis Pipeline
   */
  private async performThreeStageAnalysis(state: EnhancedAutonomousState): Promise<void> {
    console.log('🔬 PHASE 3: Three-stage analysis pipeline');
    state.currentPhase = "analysis_pipeline";

    if (state.scrapedPages.length === 0) {
      throw new Error('No scraped pages available for analysis');
    }

    try {
      // Stage 1: Topic Understanding & Context Analysis
      console.log('📚 Stage 1: Topic understanding and context analysis');
      const topicAnalysis = await this.performStage1TopicAnalysis(state);

      // Stage 2: Data Points & Statistics Extraction
      console.log('📊 Stage 2: Data points and statistics extraction');
      const dataAnalysis = await this.performStage2DataAnalysis(state);

      // Stage 3: Query Generation Intelligence
      console.log('🎯 Stage 3: Query generation intelligence');
      const queryIntelligence = await this.performStage3QueryIntelligence(state);

      // Combine all analysis results
      state.intelligenceAnalysis = {
        topicAnalysis,
        dataAnalysis,
        queryIntelligence
      };

      console.log('✅ Three-stage analysis complete');
      state.logs.push('Completed comprehensive three-stage analysis pipeline');

    } catch (error) {
      console.error('❌ Analysis pipeline failed:', error);
      state.errors.push(`Analysis pipeline failed: ${error}`);
      throw error;
    }
  }

  /**
   * Stage 1: Topic Understanding & Context Analysis
   */
  private async performStage1TopicAnalysis(state: EnhancedAutonomousState): Promise<any> {
    const analysisPrompt = `STAGE 1: TOPIC UNDERSTANDING & CONTEXT ANALYSIS

Analyze these ${state.scrapedPages.length} scraped pages to understand the topic and context for: "${state.goal}"

SCRAPED CONTENT ANALYSIS:
${state.scrapedPages.map((page, index) => `
PAGE ${index + 1}: ${page.title}
URL: ${page.url}
CONTENT: ${page.content.substring(0, 1500)}...
`).join('\n\n')}

ANALYSIS TASKS:
1. **Primary Topic Identification**: What is the main topic these pages are addressing?
2. **Topic Variations**: What different angles/variations of this topic do you see?
3. **Article Type Detection**: What type of content is this (alternatives, guide, tutorial, listicle, comparison, etc.)?
4. **Target Audience**: Who is the primary audience for this content?
5. **Content Complexity**: What level of expertise is assumed (beginner/intermediate/advanced)?
6. **Purpose Identification**: What is the main purpose/intent of this content?

Return ONLY valid JSON:
{
  "primaryTopic": "Clear topic identification",
  "topicVariations": ["variation1", "variation2", "variation3"],
  "articleTypeDetected": "specific article type",
  "targetAudienceProfile": "detailed audience description",
  "contentComplexity": "beginner|intermediate|advanced",
  "purposeIdentified": "main purpose/intent"
}`;

    const response = await this.geminiService.generateContent(analysisPrompt);
    return this.parseJsonResponse(response.response, 'Stage 1 Analysis');
  }

  /**
   * Stage 2: Data Points & Statistics Extraction
   */
  private async performStage2DataAnalysis(state: EnhancedAutonomousState): Promise<any> {
    const dataPrompt = `STAGE 2: DATA POINTS & STATISTICS EXTRACTION

Analyze these scraped pages to extract key data points, statistics, and important facts for: "${state.goal}"

CONTENT FOR ANALYSIS:
${state.scrapedPages.map((page, index) => `
PAGE ${index + 1}: ${page.title}
CONTENT: ${page.content.substring(0, 1200)}...
`).join('\n\n')}

EXTRACTION TASKS:
1. **Key Statistics**: Find specific numbers, percentages, measurements, dates
2. **Important Facts**: Extract crucial factual information
3. **Trends**: Identify trends, changes, or patterns mentioned
4. **Common Data Points**: What data appears across multiple sources?

Focus on:
- Specific numbers and metrics
- Performance data  
- Market information
- Technical specifications
- User statistics
- Comparative data
- Time-based information

Return ONLY valid JSON:
{
  "keyStatistics": [
    {"statistic": "specific stat", "source": "page title", "importance": 8}
  ],
  "importantFacts": [
    {"fact": "important fact", "category": "category", "sources": ["source1"]}
  ],
  "trends": [
    {"trend": "trend description", "timeframe": "when", "significance": "why important"}
  ],
  "commonDataPoints": ["data point that appears in multiple sources"]
}`;

    const response = await this.geminiService.generateContent(dataPrompt);
    return this.parseJsonResponse(response.response, 'Stage 2 Analysis');
  }

  /**
   * Stage 3: Query Generation Intelligence
   */
  private async performStage3QueryIntelligence(state: EnhancedAutonomousState): Promise<any> {
    const queryPrompt = `STAGE 3: QUERY GENERATION INTELLIGENCE

Analyze these pages to understand what information is needed to create superior content for: "${state.goal}"

CONTENT ANALYSIS:
${state.scrapedPages.map((page, index) => `
PAGE ${index + 1}: ${page.title}
CONTENT: ${page.content.substring(0, 1000)}...
`).join('\n\n')}

INTELLIGENCE EXTRACTION:

Based on the article type and content, extract specific intelligence for query generation:

**FOR ALTERNATIVES/COMPARISONS:**
- What alternatives/options are mentioned?
- What criteria are used for comparison?
- What aspects are evaluated?
- What alternatives might be missing?

**FOR GUIDES/TUTORIALS:**
- What step categories exist?
- What common challenges are mentioned?
- What tools/resources are needed?
- What skill levels are addressed?

**FOR INFORMATIONAL CONTENT:**
- What key topics are covered?
- What subtopic areas exist?
- What expertise areas are shown?
- What knowledge gaps exist?

**UNIVERSAL INSIGHTS:**
- What questions do people commonly ask?
- What information gaps exist in current content?
- What deeper topics could be explored?
- What related concepts should be covered?

Return ONLY valid JSON with relevant sections based on content type:
{
  "alternatives": {
    "mentionedAlternatives": ["alt1", "alt2"],
    "keyComparisonPoints": ["point1", "point2"],
    "evaluationCriteria": ["criteria1", "criteria2"],
    "missingAlternatives": ["missing1", "missing2"]
  },
  "guidance": {
    "stepCategories": ["category1", "category2"],
    "commonChallenges": ["challenge1", "challenge2"],
    "toolsNeeded": ["tool1", "tool2"],
    "skillLevels": ["level1", "level2"]
  },
  "information": {
    "keyTopics": ["topic1", "topic2"],
    "subtopicAreas": ["area1", "area2"],
    "expertiseAreas": ["area1", "area2"],
    "knowledgeGaps": ["gap1", "gap2"]
  },
  "universalInsights": {
    "commonQuestions": ["question1", "question2"],
    "informationGaps": ["gap1", "gap2"],
    "deeperTopics": ["topic1", "topic2"],
    "relatedConcepts": ["concept1", "concept2"]
  }
}`;

    const response = await this.geminiService.generateContent(queryPrompt);
    return this.parseJsonResponse(response.response, 'Stage 3 Analysis');
  }

  /**
   * PHASE 4: Create Intelligent Plan Based on Analysis
   */
  private async createIntelligentPlan(state: EnhancedAutonomousState): Promise<void> {
    console.log('🧠 PHASE 4: Creating intelligent plan based on analysis');
    state.currentPhase = "intelligent_planning";

    if (!state.intelligenceAnalysis) {
      throw new Error('Intelligence analysis required for planning');
    }

    try {
      const planningPrompt = `INTELLIGENT PLANNING BASED ON COMPREHENSIVE ANALYSIS

Create an intelligent content plan for: "${state.goal}"

ANALYSIS INSIGHTS:
**Topic Analysis:**
${JSON.stringify(state.intelligenceAnalysis.topicAnalysis, null, 2)}

**Data Analysis:**
${JSON.stringify(state.intelligenceAnalysis.dataAnalysis, null, 2)}

**Query Intelligence:**
${JSON.stringify(state.intelligenceAnalysis.queryIntelligence, null, 2)}

PLANNING TASKS:
1. **Intelligent Research Queries**: Based on the analysis, what specific queries should we research?
   - High priority: Must-have information  
   - Medium priority: Important but not critical
   - Low priority: Nice-to-have additional info

2. **Content Strategy**: Based on competitor analysis, how should we structure our content?
   - What key points must be covered?
   - What sections are required?
   - What data should be included?
   - What unique angle can we take?

3. **Execution Parameters**: 
   - Appropriate word count based on competition
   - Complexity level needed
   - Required expertise areas

Create a comprehensive plan that leverages ALL the intelligence gathered:

Return ONLY valid JSON:
{
  "title": "SEO-optimized title",
  "approach": "strategic approach description",
  "articleType": "detected article type",
  "intelligentQueries": [
    {
      "query": "specific search query",
      "purpose": "why this query is needed",
      "priority": "high|medium|low",
      "expectedFindings": ["what we expect to find"]
    }
  ],
  "contentStrategy": {
    "keyPoints": ["key point 1", "key point 2"],
    "requiredSections": ["section 1", "section 2"],
    "dataToInclude": ["data type 1", "data type 2"],
    "uniqueAngle": "our unique positioning"
  },
  "targetWordCount": 2500,
  "estimatedComplexity": 7,
  "requiredExpertise": ["expertise area 1", "expertise area 2"]
}`;

      const response = await this.geminiService.generateContent(planningPrompt);
      state.intelligentPlan = this.parseJsonResponse(response.response, 'Intelligent Planning');

      console.log(`✅ Intelligent plan created: ${state.intelligentPlan?.intelligentQueries?.length || 0} queries generated`);
      state.logs.push(`Intelligent planning complete with ${state.intelligentPlan?.intelligentQueries?.length || 0} strategic queries`);

    } catch (error) {
      console.error('❌ Intelligent planning failed:', error);
      state.errors.push(`Intelligent planning failed: ${error}`);
      throw error;
    }
  }

  /**
   * PHASE 5: Execute Traditional Workflow with Intelligence
   */
  private async executeIntelligentWorkflow(state: EnhancedAutonomousState): Promise<void> {
    console.log('🚀 PHASE 5: Executing traditional workflow with intelligence');
    state.currentPhase = "intelligent_execution";

    if (!state.intelligentPlan) {
      throw new Error('Intelligent plan required for execution');
    }

    try {
      // Create enhanced AgentState with intelligence data
      const agentState = {
        topic: state.goal,
        customInstructions: `Use the comprehensive intelligence analysis to create superior content.
        
INTELLIGENCE INSIGHTS:
- Article Type: ${state.intelligenceAnalysis?.topicAnalysis.articleTypeDetected}
- Target Audience: ${state.intelligenceAnalysis?.topicAnalysis.targetAudienceProfile}
- Unique Angle: ${state.intelligentPlan.contentStrategy.uniqueAngle}

KEY REQUIREMENTS:
${state.intelligentPlan.contentStrategy.keyPoints.map(point => `- ${point}`).join('\n')}

DATA TO INCLUDE:
${state.intelligentPlan.contentStrategy.dataToInclude.map(data => `- ${data}`).join('\n')}`,
        
        targetAudience: state.intelligenceAnalysis?.topicAnalysis.targetAudienceProfile || 'general',
        contentLength: state.intelligentPlan.targetWordCount,
        tone: 'professional',
        keywords: state.intelligentPlan.contentStrategy.keyPoints,
        contentType: state.intelligenceAnalysis?.topicAnalysis.articleTypeDetected || 'article',
        
        // Enhanced research queries from intelligence analysis
        researchQueries: state.intelligentPlan.intelligentQueries.map(q => q.query),
        
        currentPhase: 'research' as any,
        completedPhases: [],
        errors: [],
        logs: [],
        taskId: `intelligent-${Date.now()}`,
        startTime: Date.now(),
        retryCount: 0,
        maxRetries: 3,
        
        // Pass intelligence data to agents
        intelligenceData: {
          scrapedPages: state.scrapedPages,
          analysis: state.intelligenceAnalysis,
          plan: state.intelligentPlan
        }
      };

      // Execute Research Phase with Intelligent Queries
      console.log('🔬 Executing intelligent research phase');
      const researchResult = await this.researchAgent.execute(agentState);
      state.research = researchResult;

      // Execute Competition Analysis with Intelligence
      console.log('🏆 Executing intelligent competition analysis');
      const competitionResult = await this.competitionAgent.execute(agentState);
      state.competition = competitionResult;

      // Execute Content Generation with Intelligence
      console.log('✍️ Executing intelligent content generation');
      const writingResult = await this.writingAgent.execute(agentState);
      state.content = writingResult;

      // Execute Quality Assessment
      console.log('🔍 Executing quality assessment');
      const qualityResult = await this.qualityAgent.execute(agentState);
      state.qualityScore = qualityResult.qualityAssessment?.overallScore || 85;

      state.finalResult = writingResult.generatedContent;
      state.isComplete = true;

      console.log('✅ Intelligent workflow execution complete');
      state.logs.push('Traditional workflow completed with intelligence enhancement');

    } catch (error) {
      console.error('❌ Intelligent workflow execution failed:', error);
      state.errors.push(`Intelligent workflow failed: ${error}`);
      throw error;
    }
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJsonResponse(response: string, stage: string): any {
    try {
      // Clean response
      const cleanResponse = response.trim()
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .replace(/^[^{]*/, '')
        .replace(/[^}]*$/, '');

      return JSON.parse(cleanResponse);
    } catch (error) {
      console.warn(`⚠️ ${stage} JSON parsing failed, using fallback`);
      
      // Return appropriate fallback based on stage
      if (stage.includes('Stage 1')) {
        return {
          primaryTopic: "Topic analysis",
          topicVariations: ["variation1", "variation2"],
          articleTypeDetected: "guide",
          targetAudienceProfile: "general audience",
          contentComplexity: "intermediate",
          purposeIdentified: "informational"
        };
      } else if (stage.includes('Stage 2')) {
        return {
          keyStatistics: [],
          importantFacts: [],
          trends: [],
          commonDataPoints: []
        };
      } else if (stage.includes('Stage 3')) {
        return {
          universalInsights: {
            commonQuestions: ["How does this work?"],
            informationGaps: ["More details needed"],
            deeperTopics: ["Advanced concepts"],
            relatedConcepts: ["Related topics"]
          }
        };
      } else {
        return {
          title: "Comprehensive Guide",
          approach: "Research-based comprehensive analysis",
          articleType: "guide",
          intelligentQueries: [
            {
              query: "detailed information about topic",
              purpose: "comprehensive coverage",
              priority: "high",
              expectedFindings: ["key information"]
            }
          ],
          contentStrategy: {
            keyPoints: ["key point 1", "key point 2"],
            requiredSections: ["introduction", "main content", "conclusion"],
            dataToInclude: ["statistics", "examples"],
            uniqueAngle: "comprehensive approach"
          },
          targetWordCount: 2500,
          estimatedComplexity: 7,
          requiredExpertise: ["general knowledge"]
        };
      }
    }
  }
} 