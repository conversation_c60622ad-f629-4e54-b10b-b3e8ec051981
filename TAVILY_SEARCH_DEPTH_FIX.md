# Tavily Search Depth Fix - Issue Resolution

## Problem Identified

The enhanced research agent was failing with Tavily API errors because it was trying to use `'deep'` as a search depth parameter, but Tavily API only accepts `'basic'` or `'advanced'`.

**Error Message:**
```
Tavily API error 400: {"detail":{"error":"Invalid search depth. Must be 'basic' or 'advanced'."}}
```

## Root Cause

The enhanced research agent was using:
```typescript
const searchResults = await this.searchService.search(query, 5, {
  searchDepth: 'deep', // ❌ Invalid - Tavily doesn't support 'deep'
  prioritizeRecent: true
});
```

## Solution Implemented

### 1. **Fixed Research Agent Search Calls**
Updated all search calls in the research agent to use `'advanced'` instead of `'deep'`:

```typescript
// Before (causing errors)
searchDepth: 'deep'

// After (working correctly)
searchDepth: 'advanced'
```

### 2. **Enhanced Search Service Mapping**
Added intelligent mapping in the search service to handle 'deep' requests:

```typescript
// Map 'deep' to 'advanced' since <PERSON><PERSON> only supports 'basic' and 'advanced'
const searchDepth = options.searchDepth === 'deep' ? 'advanced' : (options.searchDepth || 'advanced');
```

### 3. **Updated Type Definitions**
Maintained backward compatibility while adding proper handling:

```typescript
searchDepth?: 'basic' | 'advanced' | 'deep'; // 'deep' maps to 'advanced' internally
```

## Files Modified

### 1. **src/lib/agents/v2/research-agent.ts**
- Fixed `executePhaseQueries()` method
- Fixed `executeRefinedQueries()` method
- Changed all `'deep'` references to `'advanced'`

### 2. **src/lib/search.ts**
- Added mapping logic in main `search()` method
- Fixed `buildAdvancedSearchConfig()` method
- Added comments explaining the mapping

## Verification

The fix ensures that:

1. ✅ **API Compatibility**: All search depth values are valid for Tavily API
2. ✅ **Backward Compatibility**: Existing code using 'deep' still works
3. ✅ **Enhanced Functionality**: Research agent can perform advanced searches
4. ✅ **Error Prevention**: No more 400 errors from invalid search depth

## Testing

To verify the fix works:

```typescript
// These should all work now:
await searchService.search(query, 5, { searchDepth: 'basic' });    // ✅ Valid
await searchService.search(query, 5, { searchDepth: 'advanced' }); // ✅ Valid  
await searchService.search(query, 5, { searchDepth: 'deep' });     // ✅ Maps to 'advanced'
```

## Impact

### **Before Fix:**
- ❌ Research agent failing with 400 errors
- ❌ API key rotation triggered unnecessarily
- ❌ Fallback searches being used instead of advanced searches
- ❌ Reduced research quality due to basic fallback

### **After Fix:**
- ✅ Research agent working with advanced search capabilities
- ✅ Proper utilization of Tavily's advanced search features
- ✅ No unnecessary API key rotations
- ✅ Enhanced research quality with deep analysis

## Deep Research Capabilities Restored

With this fix, the enhanced research agent can now properly execute:

1. **Multi-Phase Research**: All phases execute with advanced search depth
2. **Iterative Refinement**: Refined queries use advanced search capabilities
3. **Knowledge Graph Construction**: Based on high-quality advanced search results
4. **Quality Validation**: Comprehensive analysis with advanced search data

The research agent now delivers the intended deep research capabilities without API errors.

## Prevention

To prevent similar issues in the future:

1. **API Documentation Review**: Always check API documentation for valid parameter values
2. **Parameter Validation**: Add validation for API parameters before making calls
3. **Graceful Degradation**: Implement fallback mechanisms for invalid parameters
4. **Testing**: Test with all parameter combinations before deployment

The enhanced research agent is now fully operational and ready to provide deep research capabilities as intended.
